package com.wanou.common.utils;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName HexUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/30 11:04
 */
public class HexStrUtil {

    /**
     * 补全0
     * @param srcHex
     * @param length
     * @return
     */
    public static String supplementHex(String srcHex,int length){
        if(srcHex == null){
            return "";
        }
        if(srcHex.length() < length){
            StringBuilder append = new StringBuilder();
            for (int i = 0; i < length - srcHex.length(); i++) {
                append.append("0");
            }
            return append + srcHex;
        }
        return srcHex;
    }

    /**
     * 16转2进制
     * @return
     */
    public static String hexToBinary(String hex){
        if(StrUtil.isBlank(hex) || hex.length() % 2 != 0){
            return "";
        }
        StringBuilder binaryString = new StringBuilder();
        String tem;
        for (int i = 0; i < hex.length(); i++) {
            tem = "0000" + Integer.toBinaryString(Integer.parseInt(hex.substring(i,i+1),16));
            binaryString.append(tem.substring(tem.length() - 4));
        }

        return binaryString.toString();
    }

    /**
     * 16进制字符串每个字节的异或
     * @param src 源16进制字符串
     * @return 异或结果
     */
    public static String toEachXOR(String src){
        List<String> split = Arrays.asList(StrUtil.split(src, 2));
        List<Integer> collect = split.stream().map(HexUtil::hexToInt).collect(Collectors.toList());
        Integer result = null;
        for (Integer integer : collect) {
            if(result == null){
                result = integer;
            }else {
                result = result ^ integer;
            }
        }
        if(result == null){
            return "00";
        }
        return HexUtil.toHex(result).toUpperCase();
    }

    /**
     * byte数组转16进制字符串
     * @param msg byte数组
     * @return 16进制字符串
     */
    public static String byteToHexStr(Object msg){
        return HexUtil.encodeHexStr((byte[]) msg).toUpperCase();
    }

    /**
     * byte数组转16进制字符数组
     * @param msg byte数组
     * @return 字符数组
     */
    public static char[] byteToHexCharArr(Object msg){
        return HexUtil.encodeHex((byte[]) msg);
    }

    /**
     * 获取字符数组的子串
     * @param charArr 字符数组
     * @param start 开始位置
     * @param end 结束位置
     * @return 子串
     */
    public static String getChars(char[] charArr, int start, int end){
        StringBuilder str = new StringBuilder();
        for (int i = start; i < end; i++) {
            str.append(charArr[i]);
        }
        return str.toString().toUpperCase();
    }

    /**
     * 获取随机16进制字符串
     * @param length 随机字符串长度
     * @return 随机字符串
     */
    public static String getRandomHexStr(int length){
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(supplementHex(HexUtil.toHex(RandomUtil.randomInt(0,255)),2));
        }
        return sb.toString();
    }

    /**
     * int转16进制并补全位数
     * @param src
     * @param length
     * @return
     */
    public static String toHexAndSupplement(int src, int length){
        return supplementHex(HexUtil.toHex(src),length).toUpperCase();
    }

    /**
     * long转16进制并补全位数
     * @param src
     * @param length
     * @return
     */
    public static String toHexAndSupplement(long src, int length){
        return supplementHex(HexUtil.toHex(src),length);
    }
}
